globalThis.process??={},globalThis.process.env??={};export{renderers}from"../../../../renderers.mjs";const GET=async({params:e,request:t})=>{const{path:r}=e;if(!r)return new Response("Path required",{status:400});const s=`https://polar-public-files.s3.amazonaws.com/${r}`;try{const e=await fetch(s,{headers:{"User-Agent":t.headers.get("User-Agent")||"Cloudflare-Worker",Accept:t.headers.get("Accept")||"image/*","Accept-Encoding":t.headers.get("Accept-Encoding")||"gzip, deflate, br"}});if(!e.ok)return new Response(`S3 fetch failed: ${e.status}`,{status:e.status});const r=await e.arrayBuffer(),n=e.headers.get("Content-Type")||"image/jpeg",a=new Headers({"Content-Type":n,"Content-Length":r.byteLength.toString(),"Cache-Control":"public, max-age=2592000, immutable",Expires:new Date(Date.now()+2592e6).toUTCString(),"Last-Modified":e.headers.get("Last-Modified")||(new Date).toUTCString(),"X-Content-Type-Options":"nosniff","Cross-Origin-Resource-Policy":"cross-origin",Vary:"Accept-Encoding"}),o=e.headers.get("ETag");return o&&a.set("ETag",o),new Response(r,{status:200,headers:a})}catch(e){return console.error("S3 proxy error:",e),new Response("Internal server error",{status:500})}},prerender=!1,_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};