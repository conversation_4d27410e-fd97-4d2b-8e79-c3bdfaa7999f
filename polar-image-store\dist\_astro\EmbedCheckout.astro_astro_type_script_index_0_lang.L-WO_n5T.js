var b=Object.defineProperty,g=Object.defineProperties,E=Object.getOwnPropertyDescriptors,m=Object.getOwnPropertySymbols,w=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable,f=(c,t,e)=>t in c?b(c,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):c[t]=e,u=(c,t,e)=>new Promise((i,o)=>{var r=n=>{try{a(e.next(n))}catch(d){o(d)}},s=n=>{try{a(e.throw(n))}catch(d){o(d)}},a=n=>n.done?i(n.value):Promise.resolve(n.value).then(r,s);a((e=e.apply(c,t)).next())}),y="POLAR_CHECKOUT",h=class l{constructor(t,e){this.iframe=t,this.loader=e,this.loaded=!1,this.closable=!0,this.eventTarget=new EventTarget,this.initWindowListener(),this.addEventListener("loaded",this.loadedListener.bind(this)),this.addEventListener("close",this.closeListener.bind(this)),this.addEventListener("confirmed",this.confirmedListener.bind(this)),this.addEventListener("success",this.successListener.bind(this))}static postMessage(t,e){var i;window.parent.postMessage((i=((o,r)=>{for(var s in r||(r={}))w.call(r,s)&&f(o,s,r[s]);if(m)for(var s of m(r))L.call(r,s)&&f(o,s,r[s]);return o})({},t),g(i,E({type:y}))),e)}static create(t,e){return u(this,null,function*(){const i=document.createElement("style");i.innerText=`
      .polar-loader-spinner {
        width: 20px;
        aspect-ratio: 1;
        border-radius: 50%;
        background: ${e==="dark"?"#000":"#fff"};
        box-shadow: 0 0 0 0 ${e==="dark"?"#fff":"#000"};
        animation: polar-loader-spinner-animation 1s infinite;
      }
      @keyframes polar-loader-spinner-animation {
        100% {box-shadow: 0 0 0 30px #0000}
      }
      body.polar-no-scroll {
        overflow: hidden;
      }
    `,document.head.appendChild(i);const o=document.createElement("div");o.style.position="absolute",o.style.top="50%",o.style.left="50%",o.style.transform="translate(-50%, -50%)",o.style.zIndex="2147483647",o.style.colorScheme="auto";const r=document.createElement("div");r.className="polar-loader-spinner",o.appendChild(r),document.body.classList.add("polar-no-scroll"),document.body.appendChild(o);const s=new URL(t);s.searchParams.set("embed","true"),s.searchParams.set("embed_origin",window.location.origin),e&&s.searchParams.set("theme",e);const a=s.toString(),n=document.createElement("iframe");n.src=a,n.style.position="fixed",n.style.top="0",n.style.left="0",n.style.width="100%",n.style.height="100%",n.style.border="none",n.style.zIndex="2147483647",n.style.backgroundColor="rgba(0, 0, 0, 0.5)",n.style.colorScheme="auto";const d="https://polar.sh,https://sandbox.polar.sh".split(",").join(" ");n.allow=`payment 'self' ${d}; publickey-credentials-get 'self' ${d};`,document.body.appendChild(n);const p=new l(n,o);return new Promise(v=>{p.addEventListener("loaded",()=>v(p),{once:!0})})})}static init(){document.querySelectorAll("[data-polar-checkout]").forEach(t=>{t.removeEventListener("click",l.checkoutElementClickHandler),t.addEventListener("click",l.checkoutElementClickHandler)})}close(){document.body.removeChild(this.iframe),document.body.classList.remove("polar-no-scroll")}addEventListener(t,e,i){this.eventTarget.addEventListener(t,e,i)}removeEventListener(t,e){this.eventTarget.removeEventListener(t,e)}static checkoutElementClickHandler(t){return u(this,null,function*(){t.preventDefault();let e=t.target;for(;!e.hasAttribute("data-polar-checkout");){if(!e.parentElement)return;e=e.parentElement}const i=e.getAttribute("href")||e.getAttribute("data-polar-checkout"),o=e.getAttribute("data-polar-checkout-theme");l.create(i,o)})}loadedListener(t){t.defaultPrevented||this.loaded||(document.body.removeChild(this.loader),this.loaded=!0)}closeListener(t){t.defaultPrevented||this.closable&&this.close()}confirmedListener(t){t.defaultPrevented||(this.closable=!1)}successListener(t){t.defaultPrevented||(this.closable=!0,t.detail.redirect&&(window.location.href=t.detail.successURL))}initWindowListener(){window.addEventListener("message",({data:t,origin:e})=>{"https://polar.sh,https://sandbox.polar.sh".split(",").includes(e)&&t.type===y&&this.eventTarget.dispatchEvent(new CustomEvent(t.event,{detail:t,cancelable:!0}))})}};if(typeof window<"u"&&(window.Polar={EmbedCheckout:h}),typeof document<"u"){const c=document.currentScript;c&&c.hasAttribute("data-auto-init")&&document.addEventListener("DOMContentLoaded",()=>u(void 0,null,function*(){h.init()}))}class k{constructor(){this.init()}async init(){document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>this.setupCheckout()):this.setupCheckout()}async setupCheckout(){try{h.init(),document.querySelectorAll("[data-polar-checkout]").forEach(e=>{e.addEventListener("click",async i=>{i.preventDefault();const o=e.getAttribute("data-product-id"),r=e.getAttribute("data-polar-checkout-theme")||"light",s=e.id;if(!o){console.error("Product ID not found for checkout trigger");return}try{this.showLoading(s);const a=await this.createCheckoutUrl(o);if(!a)throw new Error("Failed to create checkout URL");const n=await h.create(a,r);this.hideLoading(s),this.setupCheckoutEvents(n,s)}catch(a){console.error("Checkout error:",a),this.hideLoading(s),this.showError("Failed to open checkout. Please try again.")}})})}catch(t){console.error("Failed to initialize embed checkout:",t)}}async createCheckoutUrl(t){try{const e=await fetch("/api/checkout-url",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productId:t})});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);return(await e.json()).checkoutUrl}catch(e){return console.error("Failed to create checkout URL:",e),null}}setupCheckoutEvents(t,e){const i=(o=0)=>{e&&this.hideLoading(e);try{const r=document.querySelector(".polar-loader-spinner");if(r){r instanceof HTMLElement&&(r.style.display="none");const a=r.parentElement;a&&a instanceof HTMLElement&&(a.style.display="none");try{r.remove()}catch{}try{a&&a.parentElement&&a.remove()}catch{}}document.querySelectorAll("[data-polar-embed-overlay], .polar-embed-overlay, .polar-embed-loader").forEach(a=>{a instanceof HTMLElement&&(a.style.display="none")})}catch{}o<5&&setTimeout(()=>i(o+1),200)};t.addEventListener("success",o=>{console.log("Checkout successful:",o.detail),i(),typeof gtag<"u"&&gtag("event","purchase",{event_category:"ecommerce",event_label:"embed_checkout"}),o.detail.checkoutId?window.location.href=`/success?checkout_id=${o.detail.checkoutId}`:window.location.href="/success"}),t.addEventListener("close",o=>{console.log("Checkout closed"),i()}),t.addEventListener("loaded",o=>{console.log("Checkout loaded"),i()}),t.addEventListener("confirmed",o=>{console.log("Payment confirmed, processing...")})}showLoading(t){const e=document.getElementById(`${t}-loading`);e&&e.classList.remove("hidden")}hideLoading(t){const e=document.getElementById(`${t}-loading`);e&&e.classList.add("hidden")}showError(t){const e=document.createElement("div");e.className="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50",e.textContent=t,document.body.appendChild(e),setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},5e3)}}new k;
