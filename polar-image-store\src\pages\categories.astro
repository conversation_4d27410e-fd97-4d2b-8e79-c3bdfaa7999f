---
import Layout from '../layouts/Layout.astro';
import CategoryCard from '../components/CategoryCard.astro';
import StructuredData from '../components/StructuredData.astro';
import { createPolarClient, transformPolarProduct, generateCategoriesWithThumbnails } from '../utils/polar';
import type { LocalProduct } from '../types/polar';

// Fetch all products to generate categories
let categories: Array<{id: string, name: string, count: number, thumbnail?: string, description?: string}> = [];
let error: string | null = null;

try {
  // Get runtime environment from Cloudflare context
  const env = Astro.locals?.runtime?.env;
  const polar = createPolarClient(env);
  const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;

  if (!organizationId) {
    error = 'Organization ID not configured';
  } else {
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const allProducts: LocalProduct[] = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Generate categories with thumbnails and descriptions
    categories = generateCategoriesWithThumbnails(allProducts);
  }
} catch (err) {
  console.error('Error fetching categories:', err);
  error = 'Failed to load categories';
}

// Breadcrumb data
const breadcrumbData = {
  items: [
    { name: "Home", url: import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store' },
    { name: "Categories", url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/categories` }
  ]
};

// Structured data for categories
const categoriesStructuredData = {
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  "name": "3D Icon Categories",
  "description": "Browse our collection of 3D icons organized by categories. Find the perfect icons for your creative projects.",
  "url": `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/categories`,
  "mainEntity": {
    "@type": "ItemList",
    "numberOfItems": categories.length,
    "itemListElement": categories.map((category, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "CollectionPage",
        "name": category.name,
        "description": category.description,
        "url": `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products/category/${category.id}`,
        "numberOfItems": category.count
      }
    }))
  }
};
---

<Layout
  title="Categories - InfPik | Browse 3D Icon Collections"
  description="Explore our organized collection of 3D icons by categories. Find business, technology, education, and more high-quality 3D icons for your creative projects."
  keywords="3D icons categories, icon collections, business icons, technology icons, creative assets"
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/categories`}
>
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />
  
  <!-- Categories Collection Structured Data -->
  <StructuredData type="custom" data={categoriesStructuredData} />
  
  <div class="w-full px-4 md:px-8 py-8">
    <!-- Header Section -->
    <section class="text-center mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        Browse by Categories
      </h1>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
        Discover our carefully curated collection of 3D icons organized by categories. 
        Find the perfect icons for your business, technology, education, and creative projects.
      </p>
      <div class="mt-6 flex items-center justify-center gap-4 text-sm text-gray-500">
        <span class="flex items-center gap-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          {categories.length} Categories
        </span>
        <span class="flex items-center gap-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
          High Quality 3D Icons
        </span>
      </div>
    </section>

    <!-- Categories Grid -->
    {error ? (
      <div class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
          <svg class="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold text-gray-900 mb-4">Unable to Load Categories</h3>
        <p class="text-gray-600 mb-8">{error}</p>
        <a
          href="/"
          class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          Back to Home
        </a>
      </div>
    ) : categories.length === 0 ? (
      <div class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
          <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Categories Found</h3>
        <p class="text-gray-600 mb-8">We're working on adding more categories. Check back soon!</p>
        <a
          href="/products"
          class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          Browse All Products
        </a>
      </div>
    ) : (
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        {categories.map((category) => (
          <CategoryCard category={category} />
        ))}
      </div>
    )}

    <!-- Call to Action Section -->
    {categories.length > 0 && (
      <section class="text-center mt-16 py-12 bg-gradient-to-r from-primary-50 to-accent-50 rounded-3xl">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          Can't Find What You're Looking For?
        </h2>
        <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
          Browse all our products or use our search feature to find the perfect 3D icons for your project.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="/products"
            class="inline-flex items-center gap-2 px-8 py-4 bg-accent-600 text-white rounded-full font-semibold text-lg transition-all duration-200 hover:bg-accent-700 hover:scale-105 hover:shadow-lg"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            Browse All Products
          </a>
          <a
            href="/"
            class="inline-flex items-center gap-2 px-8 py-4 bg-white text-accent-600 border-2 border-accent-600 rounded-full font-semibold text-lg transition-all duration-200 hover:bg-accent-600 hover:text-white hover:scale-105 hover:shadow-lg"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            Search Icons
          </a>
        </div>
      </section>
    )}
  </div>
</Layout>
