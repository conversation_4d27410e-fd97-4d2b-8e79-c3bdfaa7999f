document.addEventListener("DOMContentLoaded",()=>{const s=document.getElementById("categoryScroll");if(s){let e=!1,r,c;s.addEventListener("touchstart",n=>{e=!0,r=n.touches[0].pageX-s.offsetLeft,c=s.scrollLeft}),s.addEventListener("touchend",()=>{e=!1}),s.addEventListener("touchmove",n=>{if(!e)return;const l=(n.touches[0].pageX-s.offsetLeft-r)*2;s.scrollLeft=c-l}),s.addEventListener("mousedown",n=>{e=!0,r=n.pageX-s.offsetLeft,c=s.scrollLeft,s.style.cursor="grabbing"}),s.addEventListener("mouseleave",()=>{e=!1,s.style.cursor="grab"}),s.addEventListener("mouseup",()=>{e=!1,s.style.cursor="grab"}),s.addEventListener("mousemove",n=>{if(!e)return;n.preventDefault();const l=(n.pageX-s.offsetLeft-r)*2;s.scrollLeft=c-l}),s.style.cursor="grab";const a=document.querySelectorAll(".category-tab");a.forEach(n=>{n.addEventListener("click",d=>{const l=d.currentTarget.dataset.category;a.forEach(w=>{w.classList.remove("bg-accent-500","text-white","border-accent-500","shadow-md"),w.classList.add("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500")}),n.classList.remove("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500"),n.classList.add("bg-accent-500","text-white","border-accent-500","shadow-md"),window.location.pathname==="/"?(console.log("📡 Dispatching categoryChange event for homepage:",l),window.dispatchEvent(new CustomEvent("categoryChange",{detail:{categoryId:l}}))):l==="all"?window.location.href="/products":window.location.href=`/products/category/${l}`})});const o=document.querySelectorAll(".tag-tab");o.forEach(n=>{n.addEventListener("click",d=>{const l=d.currentTarget.dataset.tag;o.forEach(f=>{f.classList.remove("bg-accent-500","text-white","border-accent-500","shadow-md"),f.classList.add("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500")}),d.currentTarget.classList.remove("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500"),d.currentTarget.classList.add("bg-accent-500","text-white","border-accent-500","shadow-md"),setTimeout(()=>{l==="all"?window.location.href="/products":window.location.href=`/products/tag/${l}`},150)})})}const i=document.getElementById("heroSearchInput"),t=document.getElementById("heroSearchResults");let v;function u(){return window.innerWidth<768}function g(e){if(u()){e.blur();const r=e.value.trim();window.openSearchModal?.(r)}}function p(e){try{const c=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(o=>o!==e);c.unshift(e);const a=c.slice(0,10);localStorage.setItem("recentSearches",JSON.stringify(a))}catch(r){console.error("Failed to save recent search:",r)}}function b(e){try{const c=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(a=>a!==e);localStorage.setItem("recentSearches",JSON.stringify(c))}catch(r){console.error("Failed to remove recent search:",r)}}function m(){try{const e=JSON.parse(localStorage.getItem("recentSearches")||"[]");if(e.length>0&&t){const r=`
            <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
              <div class="flex items-center justify-between">
                <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
                <button id="heroSearchClearRecent" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
                  Clear
                </button>
              </div>
            </div>
            <div class="p-2">
              ${e.map((a,o)=>`
                <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors ${o<e.length-1?"border-b border-primary-100":""}">
                  <button class="hero-recent-search-item flex-1 text-left" data-query="${a}">
                    <div class="flex items-center gap-3">
                      <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span class="text-primary-900 text-sm">${a}</span>
                    </div>
                  </button>
                  <button class="hero-delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${a}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              `).join("")}
            </div>

          `;t.innerHTML=r,h(),t.classList.remove("hidden"),t.querySelectorAll(".hero-recent-search-item").forEach(a=>{a.addEventListener("click",()=>{const o=a.getAttribute("data-query");o&&i&&(i.value=o,y(o))})}),t.querySelectorAll(".hero-delete-recent-search").forEach(a=>{a.addEventListener("click",o=>{o.stopPropagation();const n=a.getAttribute("data-query");n&&(b(n),m())})}),t.querySelector("#heroSearchClearRecent")?.addEventListener("click",()=>{localStorage.removeItem("recentSearches"),t?.classList.add("hidden")})}}catch(e){console.error("Failed to load recent searches:",e)}}async function y(e){if(!(!e.trim()||!t))try{t.innerHTML='<div class="p-4 text-center text-primary-500">Searching...</div>',t.classList.remove("hidden");const c=await(await fetch(`/api/search?q=${encodeURIComponent(e)}`)).json();c.results&&c.results.length>0?(p(e),x(c.results,e)):L(e)}catch(r){console.error("Search error:",r),t.innerHTML='<div class="p-4 text-center text-red-500">Search failed. Please try again.</div>'}}function h(){if(!t||!i)return;const e=i.getBoundingClientRect(),r=192,a=window.innerHeight-e.bottom;t.style.left=`${e.left}px`,t.style.width=`${e.width}px`,t.style.top=`${e.bottom+4}px`,t.style.bottom="auto";const o=Math.min(r,a-20);t.style.maxHeight=`${Math.max(150,o)}px`}function x(e,r){if(!t)return;console.log("Displaying results:",e);const c=e.map(o=>`
        <button class="hero-search-result-item w-full p-3 hover:bg-primary-50 transition-colors text-left border-b border-primary-100 last:border-b-0" data-url="${o.url||"#"}">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                ${o.image?`<img src="${o.image}" alt="${o.name||"Product"}" class="w-full h-full object-cover">`:`
                  <div class="w-full h-full bg-accent-100 flex items-center justify-center">
                    <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                `}
              </div>
              <div class="flex-1 min-w-0">
                <div class="font-medium text-primary-900 truncate">${o.name||"Unknown Product"}</div>
                <div class="text-sm text-accent-600 font-medium">$${o.price||"0"} ${o.currency||"USD"}</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </button>
      `).join(""),a=`
        <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
          <span class="text-xs text-primary-500 font-medium">Search results for "${r}"</span>
        </div>
        ${c}

      `;t.innerHTML=a,h(),t.classList.remove("hidden"),t.querySelectorAll(".hero-search-result-item").forEach(o=>{o.addEventListener("click",()=>{const n=o.getAttribute("data-url");n&&(window.location.href=n)})})}function L(e){t&&(t.innerHTML=`
        <div class="p-4 text-center">
          <div class="text-primary-500 mb-2">No products found for "${e}"</div>
          <a href="/products" class="text-accent-600 hover:text-accent-700 text-sm font-medium">Browse all products →</a>
        </div>
      `,h(),t.classList.remove("hidden"))}function S(){const e=i?.value.trim();e?(clearTimeout(v),v=setTimeout(()=>{y(e)},300)):m()}i&&(i.addEventListener("focus",e=>{u()?g(e.target):i.value.trim()||m()}),i.addEventListener("click",e=>{u()&&g(e.target)}),i.addEventListener("input",S),i.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const r=i.value.trim();r&&(u()?window.openSearchModal?.(r):(p(r),window.location.href=`/products?search=${encodeURIComponent(r)}`))}})),document.addEventListener("click",e=>{t&&i&&!t.contains(e.target)&&!i.contains(e.target)&&t.classList.add("hidden")}),window.addEventListener("resize",()=>{t&&!t.classList.contains("hidden")&&h()}),window.addEventListener("scroll",()=>{t&&!t.classList.contains("hidden")&&h()})});
