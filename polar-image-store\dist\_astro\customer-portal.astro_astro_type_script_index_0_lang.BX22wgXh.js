document.addEventListener("DOMContentLoaded",(()=>{const e=document.getElementById("customerPortalForm"),t=document.getElementById("emailForm"),n=document.getElementById("customerData"),r=document.getElementById("loadingState"),o=document.getElementById("errorState"),s=document.getElementById("backButton"),d=document.getElementById("submitButton"),i=document.getElementById("customerEmail"),a=new URLSearchParams(window.location.search).get("email");function c(){r?.classList.add("hidden"),d.disabled=!1,d.innerHTML='\n        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0118 0z" />\n        </svg>\n        Find My Orders\n      '}function l(e){c();const t=document.getElementById("errorMessage");t&&(t.textContent=e),o?.classList.remove("hidden")}function m(){o?.classList.add("hidden")}function u(e){const t=document.createElement("div");t.className="fixed top-4 right-4 bg-success-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full",t.textContent=e,document.body.appendChild(t),setTimeout((()=>{t.classList.remove("translate-x-full")}),100),setTimeout((()=>{t.classList.add("translate-x-full"),setTimeout((()=>{document.body.removeChild(t)}),300)}),3e3)}function h(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:0,maximumFractionDigits:2}).format(e/100)}a&&i&&(i.value=a,setTimeout((()=>{e?.dispatchEvent(new Event("submit"))}),100)),e?.addEventListener("submit",(async o=>{o.preventDefault();const s=new FormData(e).get("email")?.toString().trim();if(s){m(),r?.classList.remove("hidden"),d.disabled=!0,d.innerHTML='\n        <img src="/logo.svg" alt="InfPik" class="w-5 h-5 animate-pulse" />\n        Searching...\n      ';try{const e=await fetch("/api/customer-orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:s})}),r=await e.json();if(!e.ok)throw new Error(r.error||"Failed to fetch orders");!function(e){c(),t?.classList.add("hidden"),n?.classList.remove("hidden");const r=document.getElementById("customerEmail");r&&(r.textContent=e.customer.email);const o=document.getElementById("totalOrders"),s=document.getElementById("totalSpent"),d=document.getElementById("memberSince");if(o&&(o.textContent=e.orders.length.toString()),s&&e.orders.length>0){const t=e.orders.reduce(((e,t)=>e+(t.amount||0)),0);s.textContent=h(t,e.orders[0]?.currency||"USD")}if(d&&e.customer.createdAt){const t=new Date(e.customer.createdAt);d.textContent=t.toLocaleDateString("en-US",{year:"numeric",month:"short"})}(function(e){const t=document.getElementById("ordersList"),n=document.getElementById("emptyOrders");if(!e||0===e.length)return t?.classList.add("hidden"),void n?.classList.remove("hidden");n?.classList.add("hidden"),t?.classList.remove("hidden"),t&&(t.innerHTML=e.map((e=>function(e){const t=new Date(e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),n=(e.products||[]).map((e=>e.name||"Unknown Product")).join(", "),r=function(e){switch(e?.toLowerCase()){case"paid":return"bg-success-100 text-success-700";case"pending":return"bg-warning-100 text-warning-700";case"failed":return"bg-red-100 text-red-700";default:return"bg-primary-100 text-primary-700"}}(e.status);return`\n        <div class="border border-primary-200 rounded-xl p-6 mb-4 hover:shadow-md transition-shadow">\n          <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">\n            <div class="flex-1">\n              <div class="flex items-center gap-3 mb-2">\n                <div class="w-10 h-10 bg-accent-100 rounded-full flex items-center justify-center">\n                  <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 class="font-semibold text-primary-900">Order #${e.id.slice(-8)}</h4>\n                  <p class="text-sm text-primary-600">${t}</p>\n                </div>\n              </div>\n              <p class="text-primary-700 mb-2">${n}</p>\n              <div class="flex items-center gap-4">\n                <span class="text-lg font-semibold text-primary-900">\n                  ${h(e.amount,e.currency)}\n                </span>\n                <span class="px-3 py-1 ${r} rounded-full text-sm font-medium">\n                  ${function(e){switch(e?.toLowerCase()){case"paid":return"Paid";case"pending":return"Pending";case"failed":return"Failed";case"refunded":return"Refunded";default:return e||"Unknown"}}(e.status)}\n                </span>\n              </div>\n            </div>\n            <div class="flex flex-col sm:flex-row gap-2">\n              ${e.checkoutId?`\n                <a href="/success?checkout_id=${e.checkoutId}" class="btn-secondary text-sm">\n                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />\n                  </svg>\n                  View Receipt\n                </a>\n              `:""}\n              <button onclick="copyOrderId('${e.id}')" class="btn-secondary text-sm" title="Copy Order ID">\n                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />\n                </svg>\n                Copy ID\n              </button>\n            </div>\n          </div>\n        </div>\n      `}(e))).join(""))})(e.orders),function(e){const t=document.getElementById("downloadsList"),n=document.getElementById("emptyDownloads");if(!t)return;if(!e||0===e.length)return t.innerHTML="",void n?.classList.remove("hidden");n?.classList.add("hidden");const r=e.map((e=>{const t=e?.file?.size?`(${Math.round(e.file.size/1024)} KB)`:"";return`\n          <div class="flex items-center justify-between border border-primary-200 rounded-xl p-4">\n            <div class="flex items-center gap-3">\n              <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">\n                <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5 5m0 0l5-5m-5 5V4"/></svg>\n              </div>\n              <div>\n                <div class="font-medium text-primary-900">${e?.file?.name||"File"}</div>\n                <div class="text-xs text-primary-600">${t}</div>\n              </div>\n            </div>\n            <a href="${e?.file?.download?.url||e?.file?.url||"#"}" class="btn-secondary text-sm" target="_blank" rel="noopener noreferrer">\n              Download\n            </a>\n          </div>\n        `}));t.innerHTML=r.join("")}(e.benefits)}(r)}catch(e){console.error("Error fetching customer data:",e),l(e.message||"Unable to find your account. Please check your email and try again.")}}else l("Please enter your email address")})),s?.addEventListener("click",(()=>{t?.classList.remove("hidden"),n?.classList.add("hidden"),m(),c(),e?.reset()})),document.getElementById("retryButton")?.addEventListener("click",(()=>{m()})),window.copyOrderId=function(e){navigator.clipboard.writeText(e).then((()=>{u("Order ID copied to clipboard!")})).catch((()=>{const t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),u("Order ID copied to clipboard!")}))}}));