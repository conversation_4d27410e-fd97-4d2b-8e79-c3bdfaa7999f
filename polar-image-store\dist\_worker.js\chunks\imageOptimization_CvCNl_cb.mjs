function getOptimizedImageUrl(t,e={}){if(t.startsWith("/")||t.includes("placeholder"))return t;const{width:i,height:a,quality:r=85,format:s="auto",fit:o="scale-down",sharpen:n,blur:h,saturation:p,brightness:u,contrast:m,gamma:g}=e,c=[];i&&c.push(`width=${i}`),a&&c.push(`height=${a}`),r&&c.push(`quality=${r}`),s&&c.push(`format=${s}`),o&&c.push(`fit=${o}`),n&&c.push(`sharpen=${n}`),h&&c.push(`blur=${h}`),p&&c.push(`saturation=${p}`),u&&c.push(`brightness=${u}`),m&&c.push(`contrast=${m}`),g&&c.push(`gamma=${g}`);return`/cdn-cgi/image/${c.join(",")}/${t}`}function getResponsiveImageUrls(t,e={}){const{sizes:i=[320,640,960,1280,1920],densities:a=[1,2],...r}=e,s=getOptimizedImageUrl(t,{...r,width:Math.max(...i)}),o=[];for(const e of i)for(const i of a){const a=e*i,s=getOptimizedImageUrl(t,{...r,width:a});o.push(`${s} ${a}w`)}return{src:s,srcset:o.join(", ")}}globalThis.process??={},globalThis.process.env??={};const ImagePresets={productCard:t=>getResponsiveImageUrls(t,{sizes:[320,480,640,800],densities:[1,2],width:800,height:600,quality:85,format:"auto",fit:"cover"}),productDetail:t=>getResponsiveImageUrls(t,{sizes:[400,600,800,1e3,1200],densities:[1,2],width:1e3,height:750,quality:90,format:"auto",fit:"contain"}),thumbnail:t=>getOptimizedImageUrl(t,{width:120,height:120,quality:85,format:"auto",fit:"cover"}),thumbnailLarge:t=>getOptimizedImageUrl(t,{width:200,height:200,quality:90,format:"auto",fit:"cover"}),hero:t=>getOptimizedImageUrl(t,{width:1920,height:1080,quality:90,format:"auto",fit:"cover"}),related:t=>getOptimizedImageUrl(t,{width:600,height:450,quality:85,format:"auto",fit:"cover"})};function generateSizesAttribute(t="general",e={}){let i;switch(t){case"productDetail":i={"(max-width: 1024px)":"100vw",...e};break;case"productCard":default:i={"(max-width: 640px)":"100vw","(max-width: 1024px)":"50vw","(max-width: 1280px)":"33vw",...e};break;case"thumbnail":i={"(max-width: 9999px)":"120px",...e};break;case"hero":i={"(max-width: 9999px)":"100vw",...e}}const a=Object.entries(i),r=a.slice(0,-1).map((([t,e])=>`${t} ${e}`)),s=a.length>0?a[a.length-1][1]:"50vw";return r.push(s),r.join(", ")}function supportsAVIF(t){if(!t)return!1;const e=t.match(/Chrome\/(\d+)/),i=t.match(/Firefox\/(\d+)/),a=t.match(/Version\/(\d+).*Safari/);return!!(e&&parseInt(e[1])>=85)||(!!(i&&parseInt(i[1])>=93)||!!(a&&parseInt(a[1])>=16))}function supportsWebP(t){if(!t)return!1;const e=t.match(/Chrome\/(\d+)/),i=t.match(/Firefox\/(\d+)/),a=t.match(/Version\/(\d+).*Safari/),r=t.match(/Edge\/(\d+)/);return!!(e&&parseInt(e[1])>=23)||(!!(i&&parseInt(i[1])>=65)||(!!(a&&parseInt(a[1])>=14)||!!(r&&parseInt(r[1])>=18)))}function getOptimalFormat(t){return supportsAVIF(t)?"avif":supportsWebP(t)?"webp":"jpeg"}export{ImagePresets,generateSizesAttribute,getOptimalFormat,getOptimizedImageUrl,getResponsiveImageUrls,supportsAVIF,supportsWebP};