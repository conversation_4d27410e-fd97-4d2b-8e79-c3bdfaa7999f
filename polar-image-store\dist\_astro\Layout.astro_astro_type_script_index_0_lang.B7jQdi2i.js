document.addEventListener("DOMContentLoaded",()=>{const c=document.getElementById("mobile-menu-button"),v=document.getElementById("mobile-menu");c&&v&&c.addEventListener("click",()=>{const e=!v.classList.contains("hidden");v.classList.toggle("hidden");const t=document.querySelector(".hero-search-container"),n=document.getElementById("heroSearchResults");t&&(e?t.style.display="":(t.style.display="none",n&&n.classList.add("hidden")))});const l=document.getElementById("headerSearchContainer"),d=document.getElementById("mobileHeaderSearchContainer");function f(e){l&&(e?(l.classList.add("opacity-0","-translate-y-2"),l.classList.remove("opacity-100","translate-y-0")):(l.classList.remove("opacity-0","-translate-y-2"),l.classList.add("opacity-100","translate-y-0"))),d&&(e?(d.classList.add("opacity-0","-translate-y-2"),d.classList.remove("opacity-100","translate-y-0")):(d.classList.remove("opacity-0","-translate-y-2"),d.classList.add("opacity-100","translate-y-0")))}function x(){const e=document.querySelector(".hero-search-container");if(!e){f(!1);return}f(!0);let t=!1;function n(){const i=e.getBoundingClientRect().bottom>0;f(i),t=!1}function r(){t||(requestAnimationFrame(n),t=!0)}window.addEventListener("scroll",r,{passive:!0}),n()}x();const o=document.getElementById("productSearch"),u=document.getElementById("mobileProductSearch"),s=document.getElementById("searchResults");let p;function h(){return window.innerWidth<768}function m(e){if(h()){e.blur();const t=e.value.trim();window.openSearchModal?.(t)}}function g(e){try{const n=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(a=>a!==e);n.unshift(e);const r=n.slice(0,10);localStorage.setItem("recentSearches",JSON.stringify(r))}catch(t){console.error("Failed to save recent search:",t)}}function y(){try{const e=JSON.parse(localStorage.getItem("recentSearches")||"[]");if(e.length>0&&s){const t=`
            <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
              <div class="flex items-center justify-between">
                <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
                <button id="clearRecentSearches" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
                  Clear
                </button>
              </div>
            </div>
            <div class="p-2">
              ${e.map((r,a)=>`
                <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors ${a<e.length-1?"border-b border-primary-100":""}">
                  <button class="recent-search-item flex-1 text-left" data-query="${r}">
                    <div class="flex items-center gap-3">
                      <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span class="text-primary-900 text-sm">${r}</span>
                    </div>
                  </button>
                  <button class="delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${r}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              `).join("")}
            </div>
          `;s.innerHTML=t,s.classList.remove("hidden"),s.querySelectorAll(".recent-search-item").forEach(r=>{r.addEventListener("click",()=>{const a=r.getAttribute("data-query");a&&o&&(o.value=a,w(a))})}),s.querySelectorAll(".delete-recent-search").forEach(r=>{r.addEventListener("click",a=>{a.stopPropagation();const i=r.getAttribute("data-query");i&&(b(i),y())})}),s.querySelector("#clearRecentSearches")?.addEventListener("click",()=>{localStorage.removeItem("recentSearches"),s?.classList.add("hidden")})}}catch(e){console.error("Failed to load recent searches:",e)}}function b(e){try{const n=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(r=>r!==e);localStorage.setItem("recentSearches",JSON.stringify(n))}catch(t){console.error("Failed to remove recent search:",t)}}function w(e){g(e),S({value:e})}async function S(e){const t=e.value.trim();p&&clearTimeout(p),t.length>2?s&&(s.classList.remove("hidden"),s.innerHTML=`
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <img src="/logo.svg" alt="InfPik" class="w-4 h-4 animate-pulse" />
                <span class="text-sm">Searching products for "${t}"...</span>
              </div>
            </div>
          `,p=setTimeout(async()=>{try{const r=await(await fetch(`/api/search?q=${encodeURIComponent(t)}`)).json();if(s&&!s.classList.contains("hidden"))if(r.results&&r.results.length>0){const a=r.results.map(i=>`
                    <button onclick="window.location.href='${i.url||"#"}'" class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0 text-left">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <div class="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                            ${i.image?`<img src="${i.image}" alt="${i.name||"Product"}" class="w-full h-full object-cover">`:`
                              <div class="w-full h-full bg-accent-100 flex items-center justify-center">
                                <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                              </div>
                            `}
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="text-primary-900 font-medium truncate">${i.name||"Unknown Product"}</div>
                            <div class="text-sm text-accent-600 font-medium">$${i.price||"0"} ${i.currency||"USD"}</div>
                          </div>
                        </div>
                        <svg class="w-4 h-4 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </button>
                  `).join("");s.innerHTML=`
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Search results for "${t}"
                      </div>
                      ${a}
                      ${r.total>r.results.length?`
                        <div class="p-3 border-t border-primary-100">
                          <div class="text-center text-primary-600 text-sm">
                            Showing ${r.results.length} of ${r.total} products
                          </div>
                        </div>
                      `:""}
                    </div>
                  `}else s.innerHTML=`
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No products found for "${t}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all products →
                      </a>
                    </div>
                  `}catch(n){console.error("Tag search error:",n),s&&!s.classList.contains("hidden")&&(s.innerHTML=`
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `)}},300)):y()}o&&(o.addEventListener("focus",e=>{m(e.target)}),o.addEventListener("click",e=>{m(e.target)}),o.addEventListener("input",e=>{h()||S(e.target)}),o.addEventListener("focus",e=>{!h()&&!e.target.value.trim()&&y()}),o.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const t=e.target.value.trim();t&&(h()?window.openSearchModal?.(t):(g(t),window.location.href=`/products?search=${encodeURIComponent(t)}`))}})),u&&(u.addEventListener("focus",e=>{m(e.target)}),u.addEventListener("click",e=>{m(e.target)}),u.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const t=e.target.value.trim();t&&window.openSearchModal?.(t)}})),document.addEventListener("click",e=>{s&&!o?.contains(e.target)&&!s.contains(e.target)&&s.classList.add("hidden")})});"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(c=>{console.log("SW registered: ",c)}).catch(c=>{console.log("SW registration failed: ",c)})});
