const CACHE_VERSION="v2",CACHE_NAME="infpik-dynamic-v2",STATIC_CACHE_NAME="infpik-static-v2",CACHE_DURATIONS={HTML_PAGES:3e5,PRODUCT_PAGES:18e5,API_ROUTES:3e5,STATIC_ASSETS:864e5,IMAGES:6048e5},STATIC_RESOURCES=["/fonts/inter.woff2","/logo.svg","/favicon.svg","/manifest.json"];function isCacheFresh(t,e){if(!t)return!1;const s=t.headers.get("sw-cached-date");if(!s)return!1;const a=new Date(s).getTime();return Date.now()-a<e}function addCacheTimestamp(t){const e=t.clone(),s=new Headers(e.headers);return s.set("sw-cached-date",(new Date).toISOString()),new Response(e.body,{status:e.status,statusText:e.statusText,headers:s})}async function cacheFirstStrategy(t,e,s){try{const a=await caches.open(e),n=await a.match(t);if(n&&isCacheFresh(n,s))return n;const c=await fetch(t);if(c&&200===c.status){const e=addCacheTimestamp(c);return a.put(t,e.clone()),e}return n||c}catch(e){return console.error("Cache-first strategy failed:",e),fetch(t)}}async function networkFirstStrategy(t,e,s){try{const s=await fetch(t);if(s&&200===s.status){const a=await caches.open(e),n=addCacheTimestamp(s);return a.put(t,n.clone()),n}const a=await caches.open(e);return await a.match(t)||s}catch(a){console.log("Network failed, trying cache for:",t.url);const n=await caches.open(e),c=await n.match(t);return c&&isCacheFresh(c,s)?c:c||new Response("Network error",{status:503})}}self.addEventListener("install",(t=>{t.waitUntil(caches.open(STATIC_CACHE_NAME).then((t=>t.addAll(STATIC_RESOURCES))).then((()=>self.skipWaiting())))})),self.addEventListener("activate",(t=>{t.waitUntil(caches.keys().then((t=>Promise.all(t.map((t=>{if(!t.includes("v2"))return console.log("Deleting old cache:",t),caches.delete(t)}))))).then((()=>self.clients.claim())))})),self.addEventListener("fetch",(t=>{const{request:e}=t,s=new URL(e.url);if("GET"!==e.method)return;if(s.origin!==self.location.origin)return;const a=s.pathname;let n="network-first",c=CACHE_DURATIONS.HTML_PAGES,r=CACHE_NAME;a.match(/\.(js|css|woff2?|ttf|eot|svg|ico)$/)?(n="cache-first",c=CACHE_DURATIONS.STATIC_ASSETS,r=STATIC_CACHE_NAME):a.match(/\.(png|jpg|jpeg|webp|avif|gif)$/)?(n="cache-first",c=CACHE_DURATIONS.IMAGES,r=STATIC_CACHE_NAME):a.startsWith("/products/")?(n="network-first",c=CACHE_DURATIONS.PRODUCT_PAGES):a.startsWith("/api/")?(n="network-first",c=CACHE_DURATIONS.API_ROUTES):(n="network-first",c=CACHE_DURATIONS.HTML_PAGES),"cache-first"===n?t.respondWith(cacheFirstStrategy(e,r,c)):t.respondWith(networkFirstStrategy(e,r,c))}));