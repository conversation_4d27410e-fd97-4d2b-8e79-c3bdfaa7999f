import "./chunk-BUSYA2B4.js";

// node_modules/@polar-sh/checkout/dist/embed.js
var e = Object.defineProperty;
var t = Object.defineProperties;
var n = Object.getOwnPropertyDescriptors;
var o = Object.getOwnPropertySymbols;
var r = Object.prototype.hasOwnProperty;
var s = Object.prototype.propertyIsEnumerable;
var i = (t2, n2, o2) => n2 in t2 ? e(t2, n2, { enumerable: true, configurable: true, writable: true, value: o2 }) : t2[n2] = o2;
var a = (e3, t2, n2) => new Promise((o2, r2) => {
  var s2 = (e4) => {
    try {
      a2(n2.next(e4));
    } catch (e5) {
      r2(e5);
    }
  }, i2 = (e4) => {
    try {
      a2(n2.throw(e4));
    } catch (e5) {
      r2(e5);
    }
  }, a2 = (e4) => e4.done ? o2(e4.value) : Promise.resolve(e4.value).then(s2, i2);
  a2((n2 = n2.apply(e3, t2)).next());
});
var d = "POLAR_CHECKOUT";
var l = class e2 {
  constructor(e3, t2) {
    this.iframe = e3, this.loader = t2, this.loaded = false, this.closable = true, this.eventTarget = new EventTarget(), this.initWindowListener(), this.addEventListener("loaded", this.loadedListener.bind(this)), this.addEventListener("close", this.closeListener.bind(this)), this.addEventListener("confirmed", this.confirmedListener.bind(this)), this.addEventListener("success", this.successListener.bind(this));
  }
  static postMessage(e3, a2) {
    var l2;
    window.parent.postMessage((l2 = ((e4, t2) => {
      for (var n2 in t2 || (t2 = {})) r.call(t2, n2) && i(e4, n2, t2[n2]);
      if (o) for (var n2 of o(t2)) s.call(t2, n2) && i(e4, n2, t2[n2]);
      return e4;
    })({}, e3), t(l2, n({ type: d }))), a2);
  }
  static create(t2, n2) {
    return a(this, null, function* () {
      const o2 = document.createElement("style");
      o2.innerText = `
      .polar-loader-spinner {
        width: 20px;
        aspect-ratio: 1;
        border-radius: 50%;
        background: ${"dark" === n2 ? "#000" : "#fff"};
        box-shadow: 0 0 0 0 ${"dark" === n2 ? "#fff" : "#000"};
        animation: polar-loader-spinner-animation 1s infinite;
      }
      @keyframes polar-loader-spinner-animation {
        100% {box-shadow: 0 0 0 30px #0000}
      }
      body.polar-no-scroll {
        overflow: hidden;
      }
    `, document.head.appendChild(o2);
      const r2 = document.createElement("div");
      r2.style.position = "absolute", r2.style.top = "50%", r2.style.left = "50%", r2.style.transform = "translate(-50%, -50%)", r2.style.zIndex = "2147483647", r2.style.colorScheme = "auto";
      const s2 = document.createElement("div");
      s2.className = "polar-loader-spinner", r2.appendChild(s2), document.body.classList.add("polar-no-scroll"), document.body.appendChild(r2);
      const i2 = new URL(t2);
      i2.searchParams.set("embed", "true"), i2.searchParams.set("embed_origin", window.location.origin), n2 && i2.searchParams.set("theme", n2);
      const a2 = i2.toString(), d2 = document.createElement("iframe");
      d2.src = a2, d2.style.position = "fixed", d2.style.top = "0", d2.style.left = "0", d2.style.width = "100%", d2.style.height = "100%", d2.style.border = "none", d2.style.zIndex = "2147483647", d2.style.backgroundColor = "rgba(0, 0, 0, 0.5)", d2.style.colorScheme = "auto";
      const l2 = "https://polar.sh,https://sandbox.polar.sh".split(",").join(" ");
      d2.allow = `payment 'self' ${l2}; publickey-credentials-get 'self' ${l2};`, document.body.appendChild(d2);
      const c = new e2(d2, r2);
      return new Promise((e3) => {
        c.addEventListener("loaded", () => e3(c), { once: true });
      });
    });
  }
  static init() {
    document.querySelectorAll("[data-polar-checkout]").forEach((t2) => {
      t2.removeEventListener("click", e2.checkoutElementClickHandler), t2.addEventListener("click", e2.checkoutElementClickHandler);
    });
  }
  close() {
    document.body.removeChild(this.iframe), document.body.classList.remove("polar-no-scroll");
  }
  addEventListener(e3, t2, n2) {
    this.eventTarget.addEventListener(e3, t2, n2);
  }
  removeEventListener(e3, t2) {
    this.eventTarget.removeEventListener(e3, t2);
  }
  static checkoutElementClickHandler(t2) {
    return a(this, null, function* () {
      t2.preventDefault();
      let n2 = t2.target;
      for (; !n2.hasAttribute("data-polar-checkout"); ) {
        if (!n2.parentElement) return;
        n2 = n2.parentElement;
      }
      const o2 = n2.getAttribute("href") || n2.getAttribute("data-polar-checkout"), r2 = n2.getAttribute("data-polar-checkout-theme");
      e2.create(o2, r2);
    });
  }
  loadedListener(e3) {
    e3.defaultPrevented || this.loaded || (document.body.removeChild(this.loader), this.loaded = true);
  }
  closeListener(e3) {
    e3.defaultPrevented || this.closable && this.close();
  }
  confirmedListener(e3) {
    e3.defaultPrevented || (this.closable = false);
  }
  successListener(e3) {
    e3.defaultPrevented || (this.closable = true, e3.detail.redirect && (window.location.href = e3.detail.successURL));
  }
  initWindowListener() {
    window.addEventListener("message", ({ data: e3, origin: t2 }) => {
      "https://polar.sh,https://sandbox.polar.sh".split(",").includes(t2) && (e3.type === d && this.eventTarget.dispatchEvent(new CustomEvent(e3.event, { detail: e3, cancelable: true })));
    });
  }
};
if ("undefined" != typeof window && (window.Polar = { EmbedCheckout: l }), "undefined" != typeof document) {
  const e3 = document.currentScript;
  e3 && e3.hasAttribute("data-auto-init") && document.addEventListener("DOMContentLoaded", () => a(void 0, null, function* () {
    l.init();
  }));
}
export {
  l as PolarEmbedCheckout
};
//# sourceMappingURL=@polar-sh_checkout_embed.js.map
