globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,m as maybeRenderHead,b as addAttribute,r as renderComponent,d as renderTemplate}from"./astro/server_BgKLHZ62.mjs";import{$ as $$OptimizedImage,a as $$EmbedCheckout}from"./StructuredData_3bnzjP9P.mjs";const $$Astro=createAstro("https://infpik.store"),$$ProductCard=createComponent(((e,t,r)=>{const o=e.createAstro($$Astro,t,r);o.self=$$ProductCard;const{product:a}=o.props;return renderTemplate`${maybeRenderHead()}<div class="group bg-white rounded-3xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-500 hover:-translate-y-3 hover:shadow-2xl hover:shadow-primary-500/10"> ${a.images.length>0&&renderTemplate`<div class="relative overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50"> <!-- Clickable image area that navigates to product detail --> <a${addAttribute(`/products/${a.slug}`,"href")} class="block w-full h-full"> ${renderComponent(e,"OptimizedImage",$$OptimizedImage,{src:a.images[0],alt:a.name,preset:"productCard",sizesContext:"productCard",loading:"eager",fetchpriority:"high",class:"w-full h-auto object-cover transition-all duration-500 group-hover:scale-110"})} </a> <!-- Gradient overlay --> <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div> <!-- Hover actions --> <div class="absolute inset-0 flex items-center justify-center gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0 pointer-events-none"> <a${addAttribute(`/products/${a.slug}`,"href")} class="flex items-center gap-2 px-6 py-3 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-semibold text-sm transition-all duration-200 hover:bg-white hover:scale-105 hover:shadow-lg pointer-events-auto" onclick="event.stopPropagation()"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path> </svg>
Preview
</a> <div class="pointer-events-auto" onclick="event.stopPropagation()"> ${renderComponent(e,"EmbedCheckout",$$EmbedCheckout,{productId:a.id,variant:"button",size:"sm",className:"flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold text-sm transition-all duration-200 hover:bg-accent-700 hover:scale-105 hover:shadow-lg"},{default:e=>renderTemplate`
Buy Now
`})} </div> </div> </div>`} </div>`}),"D:/code/image/polar-image-store/src/components/ProductCard.astro",void 0);export{$$ProductCard as $};