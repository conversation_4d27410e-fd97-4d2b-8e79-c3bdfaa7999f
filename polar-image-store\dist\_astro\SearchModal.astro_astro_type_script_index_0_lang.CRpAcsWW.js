class h{modal=null;backdrop=null;content=null;input=null;clearButton=null;backButton=null;searchTimeout=null;isOpen=!1;constructor(){this.init()}init(){if(this.modal=document.getElementById("searchModal"),this.backdrop=document.getElementById("searchModalBackdrop"),this.content=document.getElementById("searchModalContent"),this.input=document.getElementById("searchModalInput"),this.clearButton=document.getElementById("searchModalClearButton"),this.backButton=document.getElementById("searchModalBackButton"),!this.modal||!this.backdrop||!this.content||!this.input){console.error("SearchModal: Required elements not found");return}this.setupEventListeners(),this.loadPopularProducts()}setupEventListeners(){this.backButton?.addEventListener("click",()=>this.close()),this.backdrop?.addEventListener("click",()=>this.close()),this.clearButton?.addEventListener("click",()=>{this.input.value="",this.toggleClearButton(),this.showInitialState(),this.syncUrlToCleanState(),this.input.focus()}),this.input?.addEventListener("input",()=>{this.toggleClearButton(),this.searchTimeout&&clearTimeout(this.searchTimeout);const e=this.input.value.trim();e===""?(this.showInitialState(),this.syncUrlToCleanState()):e.length>=2&&(this.searchTimeout=setTimeout(()=>{this.performSearch(e)},500))}),this.input?.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const s=this.input.value.trim();s&&this.performSearch(s)}else e.key==="Escape"&&(e.preventDefault(),this.close())}),this.content?.addEventListener("click",e=>{e.stopPropagation()}),document.getElementById("searchModalClearRecentSearches")?.addEventListener("click",()=>{localStorage.removeItem("recentSearches"),this.hideRecentSearches()})}open(t=""){!this.modal||this.isOpen||(this.isOpen=!0,this.modal.classList.remove("hidden"),t&&this.input?(this.input.value=t,this.toggleClearButton(),t.length>=2&&this.performSearch(t)):(this.showInitialState(),this.loadRecentSearches()),requestAnimationFrame(()=>{this.backdrop?.classList.add("opacity-100"),this.backdrop?.classList.remove("opacity-0"),this.content?.classList.add("translate-x-0"),this.content?.classList.remove("translate-x-full")}),setTimeout(()=>{this.input?.focus()},300),document.body.style.overflow="hidden")}close(){!this.modal||!this.isOpen||(this.isOpen=!1,this.backdrop?.classList.add("opacity-0"),this.backdrop?.classList.remove("opacity-100"),this.content?.classList.add("translate-x-full"),this.content?.classList.remove("translate-x-0"),setTimeout(()=>{this.modal?.classList.add("hidden"),this.input&&(this.input.value=""),this.toggleClearButton(),this.showInitialState(),this.syncUrlToCleanState()},300),document.body.style.overflow="")}toggleClearButton(){!this.clearButton||!this.input||(this.clearButton.style.display=this.input.value.trim()?"flex":"none")}showInitialState(){const t=document.getElementById("searchModalResults"),e=document.getElementById("searchModalInitialState");t?.classList.add("hidden"),e?.classList.remove("hidden")}showResults(){const t=document.getElementById("searchModalResults"),e=document.getElementById("searchModalInitialState");t?.classList.remove("hidden"),e?.classList.add("hidden")}syncUrlToCleanState(){window.location.search&&window.history.replaceState({},"",window.location.pathname)}async performSearch(t){this.saveRecentSearch(t),this.showResults();const e=`${window.location.pathname}?q=${encodeURIComponent(t)}`;window.history.replaceState({query:t},"",e);try{const n=await(await fetch(`/api/search?q=${encodeURIComponent(t)}`)).json();n.error?this.showError(n.error):this.displayResults(t,n.results||[])}catch(s){console.error("Search error:",s),this.showError("Failed to search products")}}displayResults(t,e){const s=document.getElementById("searchModalResultsText"),n=document.getElementById("searchModalError"),o=document.getElementById("searchModalResultsList"),a=document.getElementById("searchModalNoResults");n?.classList.add("hidden"),s&&(s.textContent=e.length>0?`Found ${e.length} product${e.length===1?"":"s"} for "${t}"`:`No results for "${t}"`),e.length>0?(this.showResultsList(t,e),a?.classList.add("hidden")):(this.showNoResults(t),o?.classList.add("hidden"))}showResultsList(t,e){const s=document.getElementById("searchModalResultsList"),n=document.getElementById("searchModalResultsListHeader"),o=document.getElementById("searchModalResultsItems"),a=document.getElementById("searchModalResultsFooter"),i=document.getElementById("searchModalResultsCount");!s||!o||(s.classList.remove("hidden"),n&&(n.textContent=`Search results for "${t}"`),o.innerHTML=e.map((r,d)=>`
        <a
          href="${r.url}"
          class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors text-left ${d<e.length-1?"border-b border-primary-100":""}"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                ${r.image?`<img src="${r.image}" alt="${r.name}" class="w-full h-full object-cover">`:`
                  <div class="w-full h-full bg-accent-100 flex items-center justify-center">
                    <svg class="w-6 h-6 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                `}
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-primary-900 font-medium truncate">${r.name}</div>
                <div class="text-accent-600 text-sm font-medium">$${r.price} ${r.currency}</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </a>
      `).join(""),e.length>=8&&a&&i?(a.classList.remove("hidden"),i.textContent=`Showing ${e.length} results`):a?.classList.add("hidden"))}showNoResults(t){const e=document.getElementById("searchModalNoResults"),s=document.getElementById("searchModalNoResultsText"),n=document.getElementById("searchModalSuggestions"),o=document.getElementById("searchModalSuggestionsContainer");if(e&&(e.classList.remove("hidden"),s&&(s.textContent=`No results for "${t}"`),n&&o)){const a=this.getPopularProducts();a.length>0?(n.classList.remove("hidden"),o.innerHTML=a.slice(0,6).map(i=>`
            <button
              class="product-suggestion px-3 py-1 bg-primary-50 text-primary-700 rounded-full text-sm hover:bg-primary-100 transition-colors border border-primary-200"
              data-product-name="${i.name}"
            >
              ${i.name}
            </button>
          `).join(""),o.querySelectorAll(".product-suggestion").forEach(i=>{i.addEventListener("click",()=>{const r=i.getAttribute("data-product-name");r&&this.input&&(this.input.value=r,this.performSearch(r))})})):n.classList.add("hidden")}}showError(t){const e=document.getElementById("searchModalError"),s=document.getElementById("searchModalErrorText"),n=document.getElementById("searchModalResultsList"),o=document.getElementById("searchModalNoResults");!e||!s||(e.classList.remove("hidden"),s.textContent=`${t}. Please try again.`,n?.classList.add("hidden"),o?.classList.add("hidden"))}saveRecentSearch(t){try{const s=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(o=>o!==t);s.unshift(t);const n=s.slice(0,10);localStorage.setItem("recentSearches",JSON.stringify(n))}catch(e){console.error("Failed to save recent search:",e)}}loadRecentSearches(){try{const t=JSON.parse(localStorage.getItem("recentSearches")||"[]"),e=document.getElementById("searchModalRecentSearches"),s=document.getElementById("searchModalRecentSearchesList");t.length>0&&e&&s&&(e.style.display="block",s.innerHTML=`
            <div class="p-2">
              ${t.map((n,o)=>`
                <button class="recent-search-item block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors text-left ${o<t.length-1?"border-b border-primary-100":""}" data-query="${n}">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="text-primary-900 font-medium truncate">${n}</div>
                        <div class="text-primary-600 text-sm">Recent search</div>
                      </div>
                    </div>
                    <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              `).join("")}
            </div>
          `,s.querySelectorAll(".recent-search-item").forEach(n=>{n.addEventListener("click",()=>{const o=n.getAttribute("data-query");o&&this.input&&(this.input.value=o,this.performSearch(o))})}))}catch(t){console.error("Failed to load recent searches:",t)}}hideRecentSearches(){const t=document.getElementById("searchModalRecentSearches");t&&(t.style.display="none")}async loadPopularProducts(){try{const e=await(await fetch("/api/search?q=")).json();e.results&&e.results.length>0&&(this.popularProductsCache=e.results,this.displayPopularProducts(e.results))}catch(t){console.error("Failed to load popular products:",t)}}displayPopularProducts(t){const e=document.getElementById("searchModalPopularTagsContainer");e&&(e.innerHTML=t.slice(0,6).map((s,n)=>`
        <a
          href="${s.url}"
          class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors text-left ${n<Math.min(t.length,6)-1?"border-b border-primary-100":""}"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                ${s.image?`<img src="${s.image}" alt="${s.name}" class="w-full h-full object-cover">`:`
                  <div class="w-full h-full bg-accent-100 flex items-center justify-center">
                    <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                `}
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-primary-900 font-medium truncate">${s.name}</div>
                <div class="text-accent-600 text-sm font-medium">$${s.price} ${s.currency}</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </a>
      `).join(""))}popularProductsCache=[];getPopularProducts(){return this.popularProductsCache}}let l=null;document.addEventListener("DOMContentLoaded",()=>{l=new h});window.openSearchModal=(c="")=>{l?.open(c)};window.closeSearchModal=()=>{l?.close()};
