/**
 * S3 Image Proxy API Route
 * Proxies S3 images through our domain to enable proper caching
 * Fixes "Cache TTL = None" issue in PageSpeed Insights
 */

import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ params, request }) => {
  const { path } = params;
  
  if (!path) {
    return new Response('Path required', { status: 400 });
  }

  // Construct S3 URL
  const s3Url = `https://polar-public-files.s3.amazonaws.com/${path}`;
  
  try {
    // Fetch from S3
    const response = await fetch(s3Url, {
      headers: {
        // Forward relevant headers
        'User-Agent': request.headers.get('User-Agent') || 'Cloudflare-Worker',
        'Accept': request.headers.get('Accept') || 'image/*',
        'Accept-Encoding': request.headers.get('Accept-Encoding') || 'gzip, deflate, br',
      }
    });

    if (!response.ok) {
      return new Response(`S3 fetch failed: ${response.status}`, { 
        status: response.status 
      });
    }

    // Get image data
    const imageData = await response.arrayBuffer();
    const contentType = response.headers.get('Content-Type') || 'image/jpeg';

    // Create response with aggressive cache headers
    const headers = new Headers({
      'Content-Type': contentType,
      'Content-Length': imageData.byteLength.toString(),
      
      // Aggressive caching for images (30 days)
      'Cache-Control': 'public, max-age=2592000, immutable',
      'Expires': new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toUTCString(),
      'Last-Modified': response.headers.get('Last-Modified') || new Date().toUTCString(),
      
      // Security headers
      'X-Content-Type-Options': 'nosniff',
      'Cross-Origin-Resource-Policy': 'cross-origin',
      
      // Performance headers
      'Vary': 'Accept-Encoding',
    });

    // Add ETag if available from S3
    const etag = response.headers.get('ETag');
    if (etag) {
      headers.set('ETag', etag);
    }

    return new Response(imageData, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('S3 proxy error:', error);
    return new Response('Internal server error', { status: 500 });
  }
};

// Prerender this route for better performance
export const prerender = false;
