---
import OptimizedImage from './OptimizedImage.astro';

export interface CategoryData {
  id: string;
  name: string;
  count: number;
  thumbnail?: string;
  description?: string;
}

export interface Props {
  category: CategoryData;
}

const { category } = Astro.props;

// Generate category description if not provided
const defaultDescription = `Explore our collection of ${category.name.toLowerCase()} 3D icons. High-quality digital assets perfect for your creative projects.`;
const description = category.description || defaultDescription;
---

<div class="group bg-white rounded-3xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-500 hover:-translate-y-3 hover:shadow-2xl hover:shadow-primary-500/10">
  <!-- Category Thumbnail -->
  <div class="relative aspect-video overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50">
    <a href={`/products/category/${category.id}`} class="block w-full h-full">
      {category.thumbnail ? (
        <OptimizedImage
          src={category.thumbnail}
          alt={`${category.name} category`}
          preset="productCard"
          sizesContext="productCard"
          loading="lazy"
          class="w-full h-full object-cover transition-all duration-500 group-hover:scale-110"
        />
      ) : (
        <!-- Fallback gradient with category icon -->
        <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-100 to-accent-100">
          <div class="text-center">
            <svg class="w-16 h-16 mx-auto text-primary-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <span class="text-sm font-medium text-primary-600">{category.name}</span>
          </div>
        </div>
      )}
    </a>

    <!-- Gradient overlay on hover -->
    <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>

    <!-- Product count badge -->
    <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm text-primary-900 px-3 py-1 rounded-full text-sm font-semibold">
      {category.count} {category.count === 1 ? 'item' : 'items'}
    </div>

    <!-- Hover action -->
    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0 pointer-events-none">
      <a
        href={`/products/category/${category.id}`}
        class="flex items-center gap-2 px-6 py-3 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-semibold text-sm transition-all duration-200 hover:bg-white hover:scale-105 hover:shadow-lg pointer-events-auto"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        Browse {category.name}
      </a>
    </div>
  </div>

  <!-- Category Info -->
  <div class="p-6">
    <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-accent-600 transition-colors duration-200">
      <a href={`/products/category/${category.id}`} class="hover:underline">
        {category.name}
      </a>
    </h3>
    <p class="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-2">
      {description}
    </p>
    <div class="flex items-center justify-between">
      <span class="text-sm text-primary-600 font-medium">
        {category.count} {category.count === 1 ? 'Product' : 'Products'}
      </span>
      <a
        href={`/products/category/${category.id}`}
        class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors duration-200 flex items-center gap-1"
      >
        View All
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
        </svg>
      </a>
    </div>
  </div>
</div>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
