globalThis.process??={},globalThis.process.env??={};import{d as defineMiddleware,s as sequence}from"./chunks/index_kkHkWl4A.mjs";import{getOptimalFormat}from"./chunks/imageOptimization_CvCNl_cb.mjs";import"./chunks/astro-designed-error-pages_BAUzKiqr.mjs";import"./chunks/astro/server_BgKLHZ62.mjs";function generateETag(e,t){return`"${btoa(e.slice(0,100)+t.getTime().toString()).slice(0,16)}"`}function getCacheDuration(e){return e.match(/\.(js|css|woff2?|ttf|eot|svg|ico)$/)||e.match(/\.(jpg|jpeg|png|webp|avif|gif)$/i)?{maxAge:31536e3,sMaxAge:31536e3}:e.startsWith("/api/")?{maxAge:300,sMaxAge:300}:e.startsWith("/products/")?{maxAge:1800,sMaxAge:1800}:{maxAge:300,sMaxAge:300}}const onRequest$2=defineMiddleware((async(e,t)=>{const{request:s,url:a}=e,i=s.headers.get("if-none-match");if(s.headers.get("if-modified-since"),a.pathname.startsWith("/cdn-cgi/image/")){const e=s.headers.get("user-agent")||"",t=s.headers.get("accept")||"";let i;i=t.includes("image/avif")?"avif":t.includes("image/webp")?"webp":getOptimalFormat(e);const n=a.pathname.split("/");if(n.length>=4){const e=n[3],t=n.slice(4).join("/");if(e.includes("format=auto")){const s=e.replace("format=auto",`format=${i}`),n=new URL(`/cdn-cgi/image/${s}/${t}`,a.origin);return Response.redirect(n.toString(),302)}}}const n=await t(),o=new Headers(n.headers),r=a.pathname,c=new Date;let g="",f=!1;if(!r.match(/\.(js|css|woff2?|ttf|eot|svg|ico|jpg|jpeg|png|webp|avif|gif)$/i)){f=!0;try{g=await n.text()}catch(e){f=!1}}let{maxAge:m,sMaxAge:d}=getCacheDuration(r);if("/trending"===r&&(m=900,d=900),r.match(/\.(js|css|woff2?|ttf|eot|svg|ico)$/))o.set("Cache-Control",`public, max-age=${m}, immutable`),o.set("Last-Modified",c.toUTCString());else if(r.match(/\.(jpg|jpeg|png|webp|avif|gif)$/i))o.set("Cache-Control",`public, max-age=${m}, immutable`),o.set("Vary","Accept"),o.set("Last-Modified",c.toUTCString());else if(r.startsWith("/api/")){if("GET"!==s.method&&"HEAD"!==s.method)o.set("Cache-Control","no-store"),o.delete("ETag"),o.delete("Last-Modified");else if(o.set("Cache-Control",`public, max-age=${m}, s-maxage=${d}, must-revalidate`),o.set("Last-Modified",c.toUTCString()),f&&g){const e=generateETag(g,c);if(o.set("ETag",e),i===e)return new Response(null,{status:304,headers:o})}}else if(r.startsWith("/products/")){if(o.set("Cache-Control",`public, max-age=${m}, s-maxage=${d}, must-revalidate`),o.set("Last-Modified",c.toUTCString()),f&&g){const e=generateETag(g,c);if(o.set("ETag",e),i===e)return new Response(null,{status:304,headers:o})}}else if(o.set("Cache-Control",`public, max-age=${m}, s-maxage=${d}, must-revalidate`),o.set("Last-Modified",c.toUTCString()),f&&g){const e=generateETag(g,c);if(o.set("ETag",e),i===e)return new Response(null,{status:304,headers:o})}return r.startsWith("/cdn-cgi/image/")&&(o.set("Vary","Accept"),o.set("Accept-CH","Viewport-Width, Width, DPR")),o.set("X-Content-Type-Options","nosniff"),o.set("X-Frame-Options","DENY"),o.set("X-XSS-Protection","1; mode=block"),o.set("Referrer-Policy","strict-origin-when-cross-origin"),"/"===r&&o.set("Link","</fonts/inter.woff2>; rel=preload; as=font; type=font/woff2; crossorigin"),f&&g?new Response(g,{status:n.status,statusText:n.statusText,headers:o}):new Response(n.body,{status:n.status,statusText:n.statusText,headers:o})})),onRequest$1=(e,t)=>(e.isPrerendered&&(e.locals.runtime??={env:process.env}),t()),onRequest=sequence(onRequest$1,onRequest$2);export{onRequest};