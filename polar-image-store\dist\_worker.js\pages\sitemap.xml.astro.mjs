globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient}from"../chunks/polar_maBmwXh0.mjs";export{renderers}from"../renderers.mjs";const GET=async({locals:e})=>{let r=[];try{const t=e?.runtime?.env,o=createPolarClient(t),s=t?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(s){const e=await o.products.list({organizationId:s,isArchived:!1}),t=e.result?.items||[],a=new Set,i=new Set;t.forEach((e=>{if(e.name){const t=e.name.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"");r.push({url:`https://infpik.store/products/${t}`,priority:"0.8",changefreq:"weekly"})}e.metadata&&Object.entries(e.metadata).forEach((([e,r])=>{e.startsWith("category:")&&"string"==typeof r&&a.add(r),e.startsWith("tag:")&&"string"==typeof r&&i.add(r)}))})),a.forEach((e=>{r.push({url:`https://infpik.store/products/category/${e}`,priority:"0.7",changefreq:"weekly"})})),i.forEach((e=>{r.push({url:`https://infpik.store/products/tag/${e}`,priority:"0.6",changefreq:"weekly"})}))}}catch(e){console.error("Error fetching dynamic pages for sitemap:",e)}const t=[{url:"https://infpik.store/",priority:"1.0",changefreq:"daily"},{url:"https://infpik.store/products/",priority:"0.9",changefreq:"daily"},{url:"https://infpik.store/trending/",priority:"0.8",changefreq:"daily"},{url:"https://infpik.store/about/",priority:"0.6",changefreq:"weekly"},{url:"https://infpik.store/privacy/",priority:"0.5",changefreq:"monthly"},{url:"https://infpik.store/terms/",priority:"0.5",changefreq:"monthly"},{url:"https://infpik.store/success/",priority:"0.3",changefreq:"yearly"},...r],o=(new Date).toISOString().split("T")[0],s=`<?xml version="1.0" encoding="UTF-8"?>\n<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"\n        xmlns:xhtml="http://www.w3.org/1999/xhtml"\n        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"\n        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">\n${t.map((e=>`  <url>\n    <loc>${e.url}</loc>\n    <lastmod>${o}</lastmod>\n    <changefreq>${e.changefreq}</changefreq>\n    <priority>${e.priority}</priority>\n  </url>`)).join("\n")}\n</urlset>`;return new Response(s,{headers:{"Content-Type":"application/xml; charset=utf-8","Cache-Control":"public, max-age=3600"}})},prerender=!1,_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};