{"version": 3, "sources": ["../../@polar-sh/checkout/dist/embed.js"], "sourcesContent": ["var e=Object.defineProperty,t=Object.defineProperties,n=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,i=(t,n,o)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o,a=(e,t,n)=>new Promise(((o,r)=>{var s=e=>{try{a(n.next(e))}catch(e){r(e)}},i=e=>{try{a(n.throw(e))}catch(e){r(e)}},a=e=>e.done?o(e.value):Promise.resolve(e.value).then(s,i);a((n=n.apply(e,t)).next())})),d=\"POLAR_CHECKOUT\",l=class e{constructor(e,t){this.iframe=e,this.loader=t,this.loaded=!1,this.closable=!0,this.eventTarget=new EventTarget,this.initWindowListener(),this.addEventListener(\"loaded\",this.loadedListener.bind(this)),this.addEventListener(\"close\",this.closeListener.bind(this)),this.addEventListener(\"confirmed\",this.confirmedListener.bind(this)),this.addEventListener(\"success\",this.successListener.bind(this))}static postMessage(e,a){var l;window.parent.postMessage((l=((e,t)=>{for(var n in t||(t={}))r.call(t,n)&&i(e,n,t[n]);if(o)for(var n of o(t))s.call(t,n)&&i(e,n,t[n]);return e})({},e),t(l,n({type:d}))),a)}static create(t,n){return a(this,null,(function*(){const o=document.createElement(\"style\");o.innerText=`\\n      .polar-loader-spinner {\\n        width: 20px;\\n        aspect-ratio: 1;\\n        border-radius: 50%;\\n        background: ${\"dark\"===n?\"#000\":\"#fff\"};\\n        box-shadow: 0 0 0 0 ${\"dark\"===n?\"#fff\":\"#000\"};\\n        animation: polar-loader-spinner-animation 1s infinite;\\n      }\\n      @keyframes polar-loader-spinner-animation {\\n        100% {box-shadow: 0 0 0 30px #0000}\\n      }\\n      body.polar-no-scroll {\\n        overflow: hidden;\\n      }\\n    `,document.head.appendChild(o);const r=document.createElement(\"div\");r.style.position=\"absolute\",r.style.top=\"50%\",r.style.left=\"50%\",r.style.transform=\"translate(-50%, -50%)\",r.style.zIndex=\"2147483647\",r.style.colorScheme=\"auto\";const s=document.createElement(\"div\");s.className=\"polar-loader-spinner\",r.appendChild(s),document.body.classList.add(\"polar-no-scroll\"),document.body.appendChild(r);const i=new URL(t);i.searchParams.set(\"embed\",\"true\"),i.searchParams.set(\"embed_origin\",window.location.origin),n&&i.searchParams.set(\"theme\",n);const a=i.toString(),d=document.createElement(\"iframe\");d.src=a,d.style.position=\"fixed\",d.style.top=\"0\",d.style.left=\"0\",d.style.width=\"100%\",d.style.height=\"100%\",d.style.border=\"none\",d.style.zIndex=\"2147483647\",d.style.backgroundColor=\"rgba(0, 0, 0, 0.5)\",d.style.colorScheme=\"auto\";const l=\"https://polar.sh,https://sandbox.polar.sh\".split(\",\").join(\" \");d.allow=`payment 'self' ${l}; publickey-credentials-get 'self' ${l};`,document.body.appendChild(d);const c=new e(d,r);return new Promise((e=>{c.addEventListener(\"loaded\",(()=>e(c)),{once:!0})}))}))}static init(){document.querySelectorAll(\"[data-polar-checkout]\").forEach((t=>{t.removeEventListener(\"click\",e.checkoutElementClickHandler),t.addEventListener(\"click\",e.checkoutElementClickHandler)}))}close(){document.body.removeChild(this.iframe),document.body.classList.remove(\"polar-no-scroll\")}addEventListener(e,t,n){this.eventTarget.addEventListener(e,t,n)}removeEventListener(e,t){this.eventTarget.removeEventListener(e,t)}static checkoutElementClickHandler(t){return a(this,null,(function*(){t.preventDefault();let n=t.target;for(;!n.hasAttribute(\"data-polar-checkout\");){if(!n.parentElement)return;n=n.parentElement}const o=n.getAttribute(\"href\")||n.getAttribute(\"data-polar-checkout\"),r=n.getAttribute(\"data-polar-checkout-theme\");e.create(o,r)}))}loadedListener(e){e.defaultPrevented||this.loaded||(document.body.removeChild(this.loader),this.loaded=!0)}closeListener(e){e.defaultPrevented||this.closable&&this.close()}confirmedListener(e){e.defaultPrevented||(this.closable=!1)}successListener(e){e.defaultPrevented||(this.closable=!0,e.detail.redirect&&(window.location.href=e.detail.successURL))}initWindowListener(){window.addEventListener(\"message\",(({data:e,origin:t})=>{\"https://polar.sh,https://sandbox.polar.sh\".split(\",\").includes(t)&&(e.type===d&&this.eventTarget.dispatchEvent(new CustomEvent(e.event,{detail:e,cancelable:!0})))}))}};if(\"undefined\"!=typeof window&&(window.Polar={EmbedCheckout:l}),\"undefined\"!=typeof document){const e=document.currentScript;e&&e.hasAttribute(\"data-auto-init\")&&document.addEventListener(\"DOMContentLoaded\",(()=>a(void 0,null,(function*(){l.init()}))))}export{l as PolarEmbedCheckout};"], "mappings": ";;;AAAA,IAAI,IAAE,OAAO;AAAb,IAA4B,IAAE,OAAO;AAArC,IAAsD,IAAE,OAAO;AAA/D,IAAyF,IAAE,OAAO;AAAlG,IAAwH,IAAE,OAAO,UAAU;AAA3I,IAA0J,IAAE,OAAO,UAAU;AAA7K,IAAkM,IAAE,CAACA,IAAEC,IAAEC,OAAID,MAAKD,KAAE,EAAEA,IAAEC,IAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAMC,GAAC,CAAC,IAAEF,GAAEC,EAAC,IAAEC;AAApR,IAAsR,IAAE,CAACC,IAAEH,IAAEC,OAAI,IAAI,QAAS,CAACC,IAAEE,OAAI;AAAC,MAAIC,KAAE,CAAAF,OAAG;AAAC,QAAG;AAAC,MAAAG,GAAEL,GAAE,KAAKE,EAAC,CAAC;AAAA,IAAC,SAAOA,IAAE;AAAC,MAAAC,GAAED,EAAC;AAAA,IAAC;AAAA,EAAC,GAAEI,KAAE,CAAAJ,OAAG;AAAC,QAAG;AAAC,MAAAG,GAAEL,GAAE,MAAME,EAAC,CAAC;AAAA,IAAC,SAAOA,IAAE;AAAC,MAAAC,GAAED,EAAC;AAAA,IAAC;AAAA,EAAC,GAAEG,KAAE,CAAAH,OAAGA,GAAE,OAAKD,GAAEC,GAAE,KAAK,IAAE,QAAQ,QAAQA,GAAE,KAAK,EAAE,KAAKE,IAAEE,EAAC;AAAE,EAAAD,IAAGL,KAAEA,GAAE,MAAME,IAAEH,EAAC,GAAG,KAAK,CAAC;AAAC,CAAE;AAA/d,IAAie,IAAE;AAAne,IAAof,IAAE,MAAMG,GAAC;AAAA,EAAC,YAAYA,IAAEH,IAAE;AAAC,SAAK,SAAOG,IAAE,KAAK,SAAOH,IAAE,KAAK,SAAO,OAAG,KAAK,WAAS,MAAG,KAAK,cAAY,IAAI,eAAY,KAAK,mBAAmB,GAAE,KAAK,iBAAiB,UAAS,KAAK,eAAe,KAAK,IAAI,CAAC,GAAE,KAAK,iBAAiB,SAAQ,KAAK,cAAc,KAAK,IAAI,CAAC,GAAE,KAAK,iBAAiB,aAAY,KAAK,kBAAkB,KAAK,IAAI,CAAC,GAAE,KAAK,iBAAiB,WAAU,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,YAAYG,IAAEG,IAAE;AAAC,QAAIE;AAAE,WAAO,OAAO,aAAaA,MAAG,CAACL,IAAEH,OAAI;AAAC,eAAQC,MAAKD,OAAIA,KAAE,CAAC,GAAG,GAAE,KAAKA,IAAEC,EAAC,KAAG,EAAEE,IAAEF,IAAED,GAAEC,EAAC,CAAC;AAAE,UAAG,EAAE,UAAQA,MAAK,EAAED,EAAC,EAAE,GAAE,KAAKA,IAAEC,EAAC,KAAG,EAAEE,IAAEF,IAAED,GAAEC,EAAC,CAAC;AAAE,aAAOE;AAAA,IAAC,GAAG,CAAC,GAAEA,EAAC,GAAE,EAAEK,IAAE,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,IAAGF,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,OAAON,IAAEC,IAAE;AAAC,WAAO,EAAE,MAAK,MAAM,aAAW;AAAC,YAAMC,KAAE,SAAS,cAAc,OAAO;AAAE,MAAAA,GAAE,YAAU;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAqI,WAASD,KAAE,SAAO,MAAM;AAAA,8BAAkC,WAASA,KAAE,SAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAA8P,SAAS,KAAK,YAAYC,EAAC;AAAE,YAAME,KAAE,SAAS,cAAc,KAAK;AAAE,MAAAA,GAAE,MAAM,WAAS,YAAWA,GAAE,MAAM,MAAI,OAAMA,GAAE,MAAM,OAAK,OAAMA,GAAE,MAAM,YAAU,yBAAwBA,GAAE,MAAM,SAAO,cAAaA,GAAE,MAAM,cAAY;AAAO,YAAMC,KAAE,SAAS,cAAc,KAAK;AAAE,MAAAA,GAAE,YAAU,wBAAuBD,GAAE,YAAYC,EAAC,GAAE,SAAS,KAAK,UAAU,IAAI,iBAAiB,GAAE,SAAS,KAAK,YAAYD,EAAC;AAAE,YAAMG,KAAE,IAAI,IAAIP,EAAC;AAAE,MAAAO,GAAE,aAAa,IAAI,SAAQ,MAAM,GAAEA,GAAE,aAAa,IAAI,gBAAe,OAAO,SAAS,MAAM,GAAEN,MAAGM,GAAE,aAAa,IAAI,SAAQN,EAAC;AAAE,YAAMK,KAAEC,GAAE,SAAS,GAAEE,KAAE,SAAS,cAAc,QAAQ;AAAE,MAAAA,GAAE,MAAIH,IAAEG,GAAE,MAAM,WAAS,SAAQA,GAAE,MAAM,MAAI,KAAIA,GAAE,MAAM,OAAK,KAAIA,GAAE,MAAM,QAAM,QAAOA,GAAE,MAAM,SAAO,QAAOA,GAAE,MAAM,SAAO,QAAOA,GAAE,MAAM,SAAO,cAAaA,GAAE,MAAM,kBAAgB,sBAAqBA,GAAE,MAAM,cAAY;AAAO,YAAMD,KAAE,4CAA4C,MAAM,GAAG,EAAE,KAAK,GAAG;AAAE,MAAAC,GAAE,QAAM,kBAAkBD,EAAC,sCAAsCA,EAAC,KAAI,SAAS,KAAK,YAAYC,EAAC;AAAE,YAAM,IAAE,IAAIN,GAAEM,IAAEL,EAAC;AAAE,aAAO,IAAI,QAAS,CAAAD,OAAG;AAAC,UAAE,iBAAiB,UAAU,MAAIA,GAAE,CAAC,GAAG,EAAC,MAAK,KAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,OAAO,OAAM;AAAC,aAAS,iBAAiB,uBAAuB,EAAE,QAAS,CAAAH,OAAG;AAAC,MAAAA,GAAE,oBAAoB,SAAQG,GAAE,2BAA2B,GAAEH,GAAE,iBAAiB,SAAQG,GAAE,2BAA2B;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,aAAS,KAAK,YAAY,KAAK,MAAM,GAAE,SAAS,KAAK,UAAU,OAAO,iBAAiB;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEH,IAAEC,IAAE;AAAC,SAAK,YAAY,iBAAiBE,IAAEH,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBE,IAAEH,IAAE;AAAC,SAAK,YAAY,oBAAoBG,IAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,4BAA4BA,IAAE;AAAC,WAAO,EAAE,MAAK,MAAM,aAAW;AAAC,MAAAA,GAAE,eAAe;AAAE,UAAIC,KAAED,GAAE;AAAO,aAAK,CAACC,GAAE,aAAa,qBAAqB,KAAG;AAAC,YAAG,CAACA,GAAE,cAAc;AAAO,QAAAA,KAAEA,GAAE;AAAA,MAAa;AAAC,YAAMC,KAAED,GAAE,aAAa,MAAM,KAAGA,GAAE,aAAa,qBAAqB,GAAEG,KAAEH,GAAE,aAAa,2BAA2B;AAAE,MAAAE,GAAE,OAAOD,IAAEE,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAE;AAAC,IAAAA,GAAE,oBAAkB,KAAK,WAAS,SAAS,KAAK,YAAY,KAAK,MAAM,GAAE,KAAK,SAAO;AAAA,EAAG;AAAA,EAAC,cAAcA,IAAE;AAAC,IAAAA,GAAE,oBAAkB,KAAK,YAAU,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,IAAAA,GAAE,qBAAmB,KAAK,WAAS;AAAA,EAAG;AAAA,EAAC,gBAAgBA,IAAE;AAAC,IAAAA,GAAE,qBAAmB,KAAK,WAAS,MAAGA,GAAE,OAAO,aAAW,OAAO,SAAS,OAAKA,GAAE,OAAO;AAAA,EAAY;AAAA,EAAC,qBAAoB;AAAC,WAAO,iBAAiB,WAAW,CAAC,EAAC,MAAKA,IAAE,QAAOH,GAAC,MAAI;AAAC,kDAA4C,MAAM,GAAG,EAAE,SAASA,EAAC,MAAIG,GAAE,SAAO,KAAG,KAAK,YAAY,cAAc,IAAI,YAAYA,GAAE,OAAM,EAAC,QAAOA,IAAE,YAAW,KAAE,CAAC,CAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAC;AAAE,IAAG,eAAa,OAAO,WAAS,OAAO,QAAM,EAAC,eAAc,EAAC,IAAG,eAAa,OAAO,UAAS;AAAC,QAAMA,KAAE,SAAS;AAAc,EAAAA,MAAGA,GAAE,aAAa,gBAAgB,KAAG,SAAS,iBAAiB,oBAAoB,MAAI,EAAE,QAAO,MAAM,aAAW;AAAC,MAAE,KAAK;AAAA,EAAC,CAAE,CAAE;AAAC;", "names": ["t", "n", "o", "e", "r", "s", "a", "i", "l", "d"]}