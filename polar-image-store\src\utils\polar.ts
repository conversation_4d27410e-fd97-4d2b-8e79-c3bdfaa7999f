import { Polar } from '@polar-sh/sdk';
import type { LocalProduct } from '../types/polar';

// Initialize Polar client with runtime environment variables
export function createPolarClient(env?: any) {
  // Try to get from runtime environment first (Cloudflare Pages), then fallback to build-time
  const accessToken = env?.POLAR_ACCESS_TOKEN || import.meta.env.POLAR_ACCESS_TOKEN;

  if (!accessToken) {
    throw new Error('POLAR_ACCESS_TOKEN is required');
  }

  return new Polar({
    accessToken,
    server: 'production' // Always use production
  });
}

// Convert Polar product to local product format
export function transformPolarProduct(polarProduct: any): LocalProduct | null {
  if (!polarProduct || !polarProduct.id || !polarProduct.name) {
    console.warn('Invalid polar product:', polarProduct);
    return null;
  }

  const firstPrice = polarProduct.prices?.[0];
  const price = firstPrice?.priceAmount || 0;
  const currency = firstPrice?.priceCurrency || 'USD';

  // Extract category from metadata and trim whitespace
  const category = polarProduct.metadata?.category ?
    polarProduct.metadata.category.trim() : null;

  // Extract tags from metadata (preferred) or fallback to description
  const metadataTags = polarProduct.metadata?.tags ?
    polarProduct.metadata.tags.split(',').map((tag: string) => tag.trim()) : [];
  const descriptionTags = extractTags(polarProduct.description || '');
  const allTags = [...metadataTags, ...descriptionTags].filter(Boolean);

  return {
    id: polarProduct.id,
    name: polarProduct.name,
    description: polarProduct.description || '',
    price: price / 100, // Convert from cents to dollars
    currency,
    images: polarProduct.medias?.map((media: any) => media.publicUrl) || [],
    slug: generateSlug(polarProduct.name),
    isAvailable: !polarProduct.isArchived,
    tags: allTags,
    category,
    createdAt: polarProduct.createdAt,
    updatedAt: polarProduct.modifiedAt || polarProduct.createdAt
  };
}

// Generate URL-friendly slug from product name
export function generateSlug(name: string): string {
  if (!name || typeof name !== 'string') {
    return '';
  }

  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

// Extract tags from description (simple implementation)
export function extractTags(description: string): string[] {
  const tagRegex = /#(\w+)/g;
  const matches = description.match(tagRegex);
  return matches ? matches.map(tag => tag.slice(1)) : [];
}

// Format price for display
export function formatPrice(price: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(price);
}

// Create checkout URL
export async function createCheckoutUrl(productId: string): Promise<string> {
  const polar = createPolarClient();

  try {
    const checkoutLink = await polar.checkoutLinks.create({
      paymentProcessor: 'stripe',
      productId,
      allowDiscountCodes: true,
      requireBillingAddress: false,
      successUrl: `${import.meta.env.PUBLIC_SITE_URL}/success`
    });

    return checkoutLink.url;
  } catch (error) {
    console.error('Failed to create checkout URL:', error);
    throw new Error('Unable to create checkout URL');
  }
}

// Extract unique categories from products
export function extractCategories(products: LocalProduct[]): string[] {
  const categories = products
    .map(product => product.category)
    .filter((category): category is string => Boolean(category))
    .filter((category, index, array) => array.indexOf(category) === index) // Remove duplicates
    .sort();

  return categories;
}

// Get products by category
export function getProductsByCategory(products: LocalProduct[], category: string): LocalProduct[] {
  if (category === 'all') {
    return products;
  }

  return products.filter(product => product.category === category);
}

// Get category display name (convert slug to readable name)
export function getCategoryDisplayName(category: string): string {
  return category
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Generate categories with counts for navigation
export function generateCategoriesWithCounts(products: LocalProduct[]): Array<{id: string, name: string, count: number}> {
  const categoryMap = new Map<string, number>();

  // Count products in each category
  products.forEach(product => {
    if (product.category) {
      const count = categoryMap.get(product.category) || 0;
      categoryMap.set(product.category, count + 1);
    }
  });

  // Convert to array and add "All" category
  const categories = Array.from(categoryMap.entries()).map(([id, count]) => ({
    id,
    name: getCategoryDisplayName(id),
    count
  }));

  // Sort by name
  categories.sort((a, b) => a.name.localeCompare(b.name));

  // Add "All" category at the beginning
  categories.unshift({
    id: 'all',
    name: 'All',
    count: products.length
  });

  return categories;
}

// Generate categories with counts and thumbnails for category page
export function generateCategoriesWithThumbnails(products: LocalProduct[]): Array<{id: string, name: string, count: number, thumbnail?: string, description?: string}> {
  const categoryMap = new Map<string, {count: number, firstProduct?: LocalProduct}>();

  // Count products and track first product for each category
  products.forEach(product => {
    if (product.category) {
      const existing = categoryMap.get(product.category);
      if (existing) {
        existing.count++;
      } else {
        categoryMap.set(product.category, {
          count: 1,
          firstProduct: product
        });
      }
    }
  });

  // Convert to array with thumbnails
  const categories = Array.from(categoryMap.entries()).map(([id, data]) => ({
    id,
    name: getCategoryDisplayName(id),
    count: data.count,
    thumbnail: data.firstProduct?.images?.[0],
    description: generateCategoryDescription(id, data.count)
  }));

  // Sort by count (descending) then by name
  categories.sort((a, b) => {
    if (b.count !== a.count) {
      return b.count - a.count; // Higher count first
    }
    return a.name.localeCompare(b.name); // Then alphabetical
  });

  return categories;
}

// Generate category description
export function generateCategoryDescription(categoryId: string, count: number): string {
  const categoryName = getCategoryDisplayName(categoryId);
  const descriptions: Record<string, string> = {
    'business': 'Professional business icons for corporate presentations, websites, and applications.',
    'technology': 'Modern technology icons perfect for tech startups, apps, and digital platforms.',
    'education': 'Educational icons ideal for learning platforms, schools, and training materials.',
    'healthcare': 'Medical and healthcare icons for hospitals, clinics, and health applications.',
    'finance': 'Financial icons for banking, investment, and fintech applications.',
    'travel': 'Travel and tourism icons for booking platforms, travel apps, and vacation websites.',
    'food': 'Food and restaurant icons perfect for culinary websites and food delivery apps.',
    'sports': 'Sports and fitness icons for athletic websites, gym apps, and sports platforms.',
    'entertainment': 'Entertainment icons for media platforms, gaming, and leisure applications.',
    'shopping': 'E-commerce and shopping icons for online stores and retail applications.'
  };

  const customDescription = descriptions[categoryId.toLowerCase()];
  if (customDescription) {
    return customDescription;
  }

  // Fallback description
  return `Explore our collection of ${categoryName.toLowerCase()} 3D icons. ${count} high-quality digital assets perfect for your creative projects.`;
}

// Extract unique tags from products
export function extractUniqueTags(products: LocalProduct[]): string[] {
  const allTags = products
    .flatMap(product => product.tags || [])
    .filter(Boolean)
    .filter((tag, index, array) => array.indexOf(tag) === index) // Remove duplicates
    .sort();

  return allTags;
}

// Get products by tag
export function getProductsByTag(products: LocalProduct[], tag: string): LocalProduct[] {
  if (tag === 'all') {
    return products;
  }

  return products.filter(product =>
    product.tags && product.tags.includes(tag)
  );
}

// Get tag display name (convert slug to readable name)
export function getTagDisplayName(tag: string): string {
  return tag
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Generate tags with counts for navigation
export function generateTagsWithCounts(products: LocalProduct[]): Array<{id: string, name: string, count: number}> {
  const tagMap = new Map<string, number>();

  // Count products for each tag
  products.forEach(product => {
    if (product.tags) {
      product.tags.forEach(tag => {
        const count = tagMap.get(tag) || 0;
        tagMap.set(tag, count + 1);
      });
    }
  });

  // Convert to array
  const tags = Array.from(tagMap.entries()).map(([id, count]) => ({
    id,
    name: getTagDisplayName(id),
    count
  }));

  // Sort by count (descending) then by name
  tags.sort((a, b) => {
    if (b.count !== a.count) {
      return b.count - a.count; // Higher count first
    }
    return a.name.localeCompare(b.name); // Then alphabetical
  });

  // Add "All" tag at the beginning
  tags.unshift({
    id: 'all',
    name: 'All Tags',
    count: products.length
  });

  return tags;
}

export async function getTrendingProducts(days: number = 7, top: number = 10, env?: any): Promise<LocalProduct[]> {
  const polar = createPolarClient(env);

  // Resolve organization ID (required by Polar Orders API)
  const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;
  if (!organizationId) {
    console.warn('getTrendingProducts: Organization ID not configured');
    return [];
  }

  // Calculate time range (client-side filtering)
  const end = new Date();
  const start = new Date(end.getTime() - days * 24 * 60 * 60 * 1000);

  // Fetch recent orders for the organization with pagination; then filter by paid + timeframe
  let orders: any[] = [];
  try {
    const pageLimit = 10; // safety cap (10 pages x 100 = up to 1000 orders)
    let page = 1;
    let maxPage = 1;

    while (page <= maxPage && page <= pageLimit) {
      const resp: any = await polar.orders.list({
        organizationId,
        limit: 100,
        page,
        sorting: ['-created_at'] as any
      } as any);

      const raw = (resp?.items ?? resp?.result?.items ?? []) as any[];
      const pagination = (resp?.pagination ?? resp?.result?.pagination) as any;
      if (typeof (pagination?.maxPage) === 'number') {
        maxPage = Math.max(maxPage, pagination.maxPage);
      }

      orders.push(...raw);

      // If sorted desc by created_at and the last item is older than start, we can stop early
      const last = raw[raw.length - 1];
      if (last) {
        const lastCreatedAt: string = (last?.createdAt || last?.created_at || '');
        if (lastCreatedAt) {
          const lastCreated = new Date(lastCreatedAt);
          if (lastCreated < start) {
            break;
          }
        }
      }

      if (!raw.length) break; // no more data
      page++;
    }

    // Filter client-side by paid + time range
    orders = orders.filter((o: any) => {
      const createdAt: string = (o?.createdAt || o?.created_at || '');
      const created = createdAt ? new Date(createdAt) : null;
      const isPaid = (o?.paid === true) || ((o?.status || '').toLowerCase() === 'paid');
      return !!created && created >= start && created <= end && isPaid;
    });
  } catch (error) {
    console.error('Failed to fetch orders for trending products', error);
    return [];
  }

  // Count products sold by product id from order
  const counts = new Map<string, number>();
  orders.forEach((order: any) => {
    const productId: string | undefined = order?.product?.id || order?.productId || order?.product_id;
    if (!productId) return;
    counts.set(productId, (counts.get(productId) || 0) + 1);
  });

  if (counts.size === 0) return [];

  // Sort by count and get top ids
  const topIds = [...counts.entries()]
    .sort((a, b) => b[1] - a[1])
    .slice(0, top)
    .map(([id]) => id);

  // Fetch product details
  const products: LocalProduct[] = [];
  await Promise.all(
    topIds.map(async (id) => {
      try {
        const p = await polar.products.get({ id });
        const transformed = transformPolarProduct(p);
        if (transformed) products.push(transformed);
      } catch (err) {
        console.warn(`Unable to fetch product ${id}:`, err);
      }
    })
  );

  return products;
}
